<?php

declare(strict_types=1);

namespace <PERSON><PERSON><PERSON>\Commands;

use <PERSON><PERSON><PERSON>\Model\Leaflets\Entities\Leaflet;
use <PERSON><PERSON><PERSON>\Model\Leaflets\LeafletFacade;
use <PERSON><PERSON><PERSON>\Model\Seo\GoogleIndexFacade;
use <PERSON><PERSON><PERSON>\Model\Shops\ShopFacade;
use <PERSON><PERSON><PERSON>\Model\Websites\WebsiteFacade;
use Nette\Application\LinkGenerator;

class ProcessGoogleIndex extends Job
{
	/** @var LeafletFacade */
	private $leafletFacade;

	/** @var GoogleIndexFacade */
	private $googleIndexFacade;

	/** @var WebsiteFacade */
	private $websiteFacade;

	/** @var LinkGenerator */
	private $linkGenerator;

	/** @var ShopFacade */
	private $shopFacade;

	public function __construct(
		LeafletFacade $leafletFacade,
		GoogleIndexFacade $googleIndexFacade,
		WebsiteFacade $websiteFacade,
		LinkGenerator $linkGenerator,
		ShopFacade $shopFacade
	) {
		parent::__construct();

		$this->leafletFacade = $leafletFacade;
		$this->googleIndexFacade = $googleIndexFacade;
		$this->websiteFacade = $websiteFacade;
		$this->linkGenerator = $linkGenerator;
		$this->shopFacade = $shopFacade;
	}

	protected function configure(): void
	{
		$this->setName('kaufino:process-google-index');
	}

	public function start(): void
	{
		$log = $this->onStart();

		$this->processGoogleIndex();

		$this->onFinish($log);
	}

	private function processGoogleIndex(): void
	{
		ini_set('memory_limit', '1024M');
		ini_set('max_execution_time', '1800'); // 30 minut

		echo "Starting Google Index processing...\n";

		$currentHour = (int) date('H');

		// 1. Přidej nové letáky do indexování (pouze po 9. hodině)
		if ($currentHour > 9) {
			$this->addNewLeafletsToIndex();
		} else {
			echo "Skipping addNewLeafletsToIndex - current hour is {$currentHour}, need > 9\n";
		}

		// 2. Zpracuj URL k indexování (pouze ty, které ještě nebyly zpracovány)
		$this->processIndexingRequests();

		// 3. Zkontroluj stav indexace (každou hodinu, max 1800 requestů za den)
		$this->checkIndexingStatus();

		// 4. Zobraz statistiky
		$this->showStatistics();

		echo "Google Index processing completed.\n";
	}

	private function addNewLeafletsToIndex(): void
	{
		echo "Adding new leaflets to index...\n";

		$leafletsAddedToday = $this->googleIndexFacade->countLeafletsAddedToday();

		if ($leafletsAddedToday >= 180) {
			echo "Skipping - already added {$leafletsAddedToday} leaflets today (max 180 per day)\n";
			return;
		}

		$maxToAdd = 180 - $leafletsAddedToday;
		$leaflets = $this->leafletFacade->findLeafletsToProcessGoogleIndex();
		$addedCount = 0;

		foreach ($leaflets as $leaflet) {
			if ($addedCount >= $maxToAdd) {
				echo "Reached daily leaflets limit ({$maxToAdd}), stopping\n";
				break;
			}

			$urls = $this->generateLeafletUrl($leaflet);

			foreach ($urls as $url) {
				if ($addedCount >= $maxToAdd) {
					break;
				}

				$existingIndex = $this->googleIndexFacade->findByUrl($url['url']);

				if (!$existingIndex) {
					$shop = $leaflet->getShop();
					$localization = $shop->getLocalization();

					$this->googleIndexFacade->addUrlToIndex($url['websiteDomain'], $url['url'], $localization, $leaflet, $shop);
					$addedCount++;
					echo "Added to index: {$url['url']}\n";
				}
			}
		}

		echo "Added {$addedCount} new URLs to index (leaflets today: " . ($leafletsAddedToday + $addedCount) . "/180).\n";
	}

	private function processIndexingRequests(): void
	{
		echo "Processing indexing requests...\n";

		$urlsToIndex = $this->googleIndexFacade->findUrlsToIndex(50);
		$processedCount = 0;
		$successCount = 0;
		$skippedCount = 0;

		echo "Found " . count($urlsToIndex) . " URLs that have never been processed for indexing.\n";

		foreach ($urlsToIndex as $googleIndex) {
			if ($googleIndex->getLastIndexingRequestAt() !== null) {
				$skippedCount++;
				echo "⏭ Skipping already processed URL: {$googleIndex->getUrl()}\n";
				continue;
			}

			if ($this->googleIndexFacade->processIndexingRequest($googleIndex)) {
				$successCount++;
				echo "✓ Indexing request sent: {$googleIndex->getUrl()}\n";
			} else {
				echo "✗ Failed to send indexing request: {$googleIndex->getUrl()}\n";
			}

			$processedCount++;
		}

		echo "Processed {$processedCount} indexing requests, {$successCount} successful, {$skippedCount} skipped.\n";
	}

	private function checkIndexingStatus(): void
	{
		echo "Checking indexing status...\n";

		$urlsToCheck = $this->googleIndexFacade->findUrlsToCheckStatus(30);
		$checkedCount = 0;
		$indexedCount = 0;
		$skippedCount = 0;

		echo "Found " . count($urlsToCheck) . " URLs to check status (hourly interval).\n";

		foreach ($urlsToCheck as $googleIndex) {
			if ($this->googleIndexFacade->checkIndexingStatus($googleIndex)) {
				if ($googleIndex->isIndexed()) {
					$indexedCount++;
					echo "✓ URL is indexed: {$googleIndex->getUrl()}\n";
				} else {
					echo "⏳ URL not indexed yet: {$googleIndex->getUrl()}\n";
				}
				$checkedCount++;
			} else {
				$skippedCount++;
				echo "⏭ Skipped status check (rate limit): {$googleIndex->getUrl()}\n";
			}
		}

		echo "Checked {$checkedCount} URLs, {$indexedCount} are indexed, {$skippedCount} skipped due to rate limits.\n";
	}

	private function showStatistics(): void
	{
		echo "\n=== Google Index Statistics ===\n";

		$stats = $this->googleIndexFacade->getStatistics();

		echo "Pending indexing: {$stats['pending_indexing']}\n";
		echo "Successfully indexed: {$stats['indexed']}\n";
		echo "Leaflets added today: {$stats['leaflets_added_today']}/180\n";
		echo "API requests today (all): {$stats['api_requests_today']}/{$stats['daily_limit']}\n";
		echo "  - Indexing requests: {$stats['indexing_requests_today']}/{$stats['daily_limit']}\n";
		echo "  - Status check requests: {$stats['status_check_requests_today']}/1800\n";
		echo "Current hour: " . date('H') . " (addNewLeafletsToIndex runs when > 9)\n";
		echo "===============================\n\n";
	}

	private function generateLeafletUrl(Leaflet $leaflet): array
	{
		$urls = [];

		$websites = $this->websiteFacade->findActiveWebsites('kaufino');

		foreach ($websites as $website) {
			$localization = $website->getLocalization();

			if ($localization->isCzech() === false) {
				continue;
			}

			$shop = $leaflet->getShop();

			$urls[] = [
				'websiteDomain' => $website->getDomain(),
				'url' => 'https://www.kaufino.com/' . $localization->getRegion() . '/' . $shop->getSlug() . '/' . $leaflet->getSlug(),
			];
		}

		return $urls;
	}
}
