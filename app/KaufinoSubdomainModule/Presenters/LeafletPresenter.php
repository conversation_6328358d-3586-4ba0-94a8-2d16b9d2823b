<?php

declare(strict_types=1);

namespace <PERSON><PERSON><PERSON>\KaufinoSubdomainModule\Presenters;

use <PERSON><PERSON><PERSON>\Forms\ContestControl\ContestControl;
use <PERSON><PERSON><PERSON>\Forms\ContestControl\ContestControlFactory;
use <PERSON><PERSON><PERSON>\Model\Leaflets\Entities\Leaflet;
use <PERSON><PERSON><PERSON>\Model\Leaflets\LeafletFacade;
use Ka<PERSON>ino\Model\Offers\Entities\Offer;
use Ka<PERSON>ino\Model\Offers\OfferFacade;
use Ka<PERSON>ino\Model\Shops\Entities\Shop;
use Ka<PERSON>ino\Model\Shops\ShopFacade;
use Ka<PERSON><PERSON>\Model\Websites\Entities\Website;
use Tracy\Debugger;

final class LeafletPresenter extends BasePresenter
{
	/** @var LeafletFacade @inject */
	public $leafletFacade;

	/** @var ShopFacade @inject */
	public $shopFacade;

	/** @var OfferFacade @inject */
	public $offerFacade;

	/** @var ContestControlFactory @inject */
	public $contestControlFactory;

	public function renderDefault(): void
	{
		$leaflets = $this->leafletFacade->findLeaflets($this->localization, false, null, Website::MODULE_KAUFINO_SUBDOMAIN);

		$this->template->leaflets = $leaflets;
	}

	public function __construct()
	{
		parent::__construct();

		$this->disableCachedResponse = true;
	}

	public function actionLeaflet(Shop $shop, Leaflet $leaflet): void
	{
		$page = (int) ($this->getParameter('page') ?? 1);

		$countOfPages = $leaflet->getCountOfPages();

		$isVirtualPage = $page === $leaflet->getCountOfPages() + 1;

		if ($isVirtualPage) {
			$countOfPages += 1;
		}

		if ($leaflet->getShop() != $shop) {
			$this->error('The leaflet is not owned by the shop.');
		}

		if ($leaflet->isDeleted() || $shop->isActiveKaufino() === false) {
			$this->redirectPermanent("Shop:shop", ['shop' => $shop]);
		}

		$this->template->isVirtualPage = $isVirtualPage;

		if ($isVirtualPage === false && $page > $countOfPages) {
			$this->redirectPermanent("Leaflet:leaflet", ['shop' => $shop, 'leaflet' => $leaflet]);
		}

		$this->template->shop = $shop;
		$this->template->leaflet = $leaflet;

		$this->template->getSimilarLeaflets = (function () use ($shop, $leaflet) {
			return $this->leafletFacade->findLeafletsByShop($shop, 5, true, true, $leaflet);
		});

		$this->template->offers = $this->offerFacade->findOffersByLeaflet($leaflet, 20, true, Offer::TYPE_LEAFLET);

		$this->template->getRecommendedLeaflets = (function () use ($leaflet) {
			if ($leaflet->isNewsletter()) {
				return $this->leafletFacade->findNewsletters($this->localization, false, 5);
			}

			return $this->leafletFacade->findLeaflets($this->localization, false, 5, Website::MODULE_KAUFINO_SUBDOMAIN);
		});

		$this->template->similarShops = $this->shopFacade->findLeafletShops($this->localization, false, null, Website::MODULE_KAUFINO_SUBDOMAIN);

		$this->responseCacheTags[] = 'shop/' . $shop->getId();

		if ($isVirtualPage) {
			$this->template->topLeaflets = $this->leafletFacade->findTopLeaflets($this->localization, true, 9, Website::MODULE_KAUFINO_SUBDOMAIN);
			$this->template->topShops =  $this->shopFacade->findTopLeafletShops($this->localization, true, 12, Website::MODULE_KAUFINO_SUBDOMAIN);
			Debugger::log($this->website->getDomain() . ': virtual page visited: ' . $leaflet->getId(), 'virtual-page-visits');
		}

		$this->template->currentPage = $page;

		$this->template->leafletDescription = $this->contentGenerator->generateLeafletDescription($leaflet);

		if ($leaflet->isNewsletter() && $shop->useLeafletTemplateForNewsletters() === false) {
			$this->setView('leafletNewsletter');
		}

		$cookieName = 'relatedLeafletOpened_' . $leaflet->getId();
		$this->template->openRelatedLeafletAllowed = $this->getHttpRequest()->getCookie($cookieName) === null;

		$usTemplateDomains = [
			'https://weeklyads.kaufino.com',
			'https://www.weekly-ads123.us',
			'https://www.catalogues123.fr',
			'https://www.folletos123.es',
			'https://www.prospekte123.de',
			'https://www.letaky123.sk',
			'https://www.letaky123.cz',
			'https://www.katalozi123.hr',
			'https://www.akcios-ujsagok123.hu',
			'https://www.folders123.nl',
			'https://italy.volantini123.it',
			'https://it.volantini123.it',
			'https://us.weekly-ads123.us',
			'https://www.tilbudsaviser123.dk',
			'https://www.cataloage123.ro',
			'https://www.flugblatter123.at',
			'https://www.gazetki123.pl',
			'https://www.folders123.be',
			'https://www.catalogues123.co.za',
			'https://www.katalozi123.rs',
			'https://www.erbjudandena123.se',
			'https://www.phylladio123.gr',
			'https://www.katalogi123.si',
			'https://www.kliendilehed123.ee',
			'https://www.folhetos123.pt',
		];

		if (in_array($this->website->getDomain(), $usTemplateDomains)) {
			$this->setView('us');
		}

		$noAdsTemplate = [
			'https://www.kundeaviser123.no',
			'https://www.brosurleri123.tr',
			'https://www.folletosar123.com.ar',
			'https://www.leidinys123.lt',
			'https://www.tarjouslehti123.fi',
			'https://www.philladio123.cy',
			'https://www.catalogos123.com.mx',
			'https://www.katalohy123.com.ua',
			'https://www.chirashi123.jp',
		];

		if (in_array($this->website->getDomain(), $noAdsTemplate)) {
			$this->setView('noAds');
		}
	}

	public function handleOpenRelatedLeaflet(int $leafletId): void
	{
		$currentLeaflet = $this->leafletFacade->findLeaflet($leafletId);

		$relatedLeaflet = $this->leafletFacade->findRelatedLeaflet($currentLeaflet);

		if ($relatedLeaflet === null) {
			$topLeaflets = $this->leafletFacade->findTopLeaflets($this->localization, true, 10, $this->website->getModule());

			foreach ($topLeaflets as $topLeaflet) {
				if ($topLeaflet->getId() !== $currentLeaflet->getId()) {
					$relatedLeaflet = $topLeaflet;
					break;
				}
			}
		}

		$this->getHttpResponse()->setCookie('relatedLeafletOpened_' . $currentLeaflet->getId(), '1', '1 day');

		if ($relatedLeaflet === null) {
			$this->redirect('Shop:shop', ['shop' => $currentLeaflet->getShop()]);
		}

		Debugger::log($this->website->getDomain() . ': redirect from: ' . $leafletId . ' to: ' . $relatedLeaflet->getId(), 'related-leaflet-redirects');

		$this->redirect('this', ['shop' => $relatedLeaflet->getShop(), 'leaflet' => $relatedLeaflet]);
	}

	protected function createComponentContestControl(): ContestControl
	{
		$control = $this->contestControlFactory->create($this->getParameter('leaflet'));

		$control->onSuccess[] = function () {
			$this->flashMessage('Děkujeme, že jste se zúčastnili naší soutěže! Váš e-mail byl úspěšně zaregistrován. Držíme vám palce – brzy se dozvíte, jestli jste vyhráli!', 'success');
			$this->redirect('this', ['page' => 'contest']);
		};

		return $control;
	}
}
