<?php

declare(strict_types=1);

namespace <PERSON><PERSON><PERSON>\Model\Seo\Repositories;

use Doctrine\ORM\EntityRepository;
use <PERSON><PERSON>ino\Model\Seo\Entities\GoogleIndex;

class GoogleIndexRepository extends EntityRepository
{
	public function findByUrl(string $url): ?GoogleIndex
	{
		return $this->findOneBy(['url' => $url]);
	}

	public function findByWebsiteDomain(string $websiteDomain, int $limit = 100): array
	{
		return $this->createQueryBuilder('gi')
			->where('gi.websiteDomain = :websiteDomain')
			->setParameter('websiteDomain', $websiteDomain)
			->setMaxResults($limit)
			->orderBy('gi.createdAt', 'DESC')
			->getQuery()
			->getResult();
	}

	public function countPendingIndexing(): int
	{
		return (int) $this->createQueryBuilder('gi')
			->select('COUNT(gi.id)')
			->where('gi.isIndexed = 0')
			->getQuery()
			->getSingleScalarResult();
	}

	public function countIndexed(): int
	{
		return (int) $this->createQueryBuilder('gi')
			->select('COUNT(gi.id)')
			->where('gi.isIndexed = 1')
			->getQuery()
			->getSingleScalarResult();
	}

	public function findRecentlyProcessed(int $limit = 100): array
	{
		return $this->createQueryBuilder('gi')
			->where('gi.lastIndexingRequestAt IS NOT NULL')
			->setMaxResults($limit)
			->orderBy('gi.lastIndexingRequestAt', 'DESC')
			->getQuery()
			->getResult();
	}

	public function countLeafletsAddedToday(): int
	{
		return (int) $this->createQueryBuilder('gi')
			->select('COUNT(gi.id)')
			->where('gi.leaflet IS NOT NULL')
			->andWhere('gi.createdAt >= :today')
			->setParameter('today', new \DateTime('today'))
			->getQuery()
			->getSingleScalarResult();
	}

	public function findUrlsToIndex(int $limit = 50): array
	{
		return $this->createQueryBuilder('gi')
			->where('gi.isIndexed = 0')
			->andWhere('gi.lastIndexingRequestAt IS NULL')
			->setMaxResults($limit)
			->orderBy('gi.createdAt', 'ASC')
			->getQuery()
			->getResult();
	}

	public function findUrlsToCheckStatus(int $limit = 50): array
	{
		return $this->createQueryBuilder('gi')
			->where('gi.isIndexed = 0')
			->andWhere('gi.lastIndexingRequestAt IS NOT NULL')
			->andWhere('gi.lastStatusCheckAt IS NULL OR gi.lastStatusCheckAt < :oneHourAgo')
			->setParameter('oneHourAgo', new \DateTime('-1 hour'))
			->setMaxResults($limit)
			->orderBy('gi.lastIndexingRequestAt', 'ASC')
			->getQuery()
			->getResult();
	}
}
