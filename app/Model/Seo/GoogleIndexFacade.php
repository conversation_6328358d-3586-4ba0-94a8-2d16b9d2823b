<?php

declare(strict_types=1);

namespace <PERSON><PERSON><PERSON>\Model\Seo;

use <PERSON><PERSON><PERSON>\Model\EntityManager;
use <PERSON><PERSON><PERSON>\Model\Google\GoogleIndexingClient;
use <PERSON><PERSON><PERSON>\Model\Leaflets\Entities\Leaflet;
use <PERSON><PERSON><PERSON>\Model\Localization\Entities\Localization;
use <PERSON><PERSON><PERSON>\Model\Seo\Entities\GoogleIndex;
use <PERSON><PERSON>ino\Model\Seo\Entities\GoogleIndexProcess;
use <PERSON><PERSON><PERSON>\Model\Seo\Repositories\GoogleIndexRepository;
use Ka<PERSON><PERSON>\Model\Seo\Repositories\GoogleIndexProcessRepository;
use <PERSON><PERSON><PERSON>\Model\Shops\Entities\Shop;
use Nette\Utils\Json;
use Tracy\Debugger;

class GoogleIndexFacade
{
	/** @var EntityManager */
	private $entityManager;

	/** @var GoogleIndexRepository */
	private $googleIndexRepository;

	/** @var GoogleIndexProcessRepository */
	private $googleIndexProcessRepository;

	/** @var GoogleIndexingClient */
	private $googleIndexingClient;

	public function __construct(
		EntityManager $entityManager,
		GoogleIndexingClient $googleIndexingClient
	) {
		$this->entityManager = $entityManager;
		$this->googleIndexingClient = $googleIndexingClient;
		$this->googleIndexRepository = $entityManager->getRepository(GoogleIndex::class);
		$this->googleIndexProcessRepository = $entityManager->getRepository(GoogleIndexProcess::class);
	}

	public function addUrlToIndex(string $websiteDomain, string $url, Localization $localization, ?Leaflet $leaflet = null, ?Shop $shop = null): GoogleIndex
	{
		$googleIndex = $this->googleIndexRepository->findByUrl($url);

		if (!$googleIndex) {
			$googleIndex = new GoogleIndex($websiteDomain, $url, $localization, $leaflet, $shop);
			$this->entityManager->persist($googleIndex);
			$this->entityManager->flush();
		}

		return $googleIndex;
	}

	public function processIndexingRequest(GoogleIndex $googleIndex): bool
	{
		$dailyIndexingCount = $this->googleIndexProcessRepository->countIndexingApiRequestsToday();

		if ($dailyIndexingCount >= $this->googleIndexingClient->getDailyLimit()) {
			Debugger::log("Daily rate limit reached for indexing. Daily: {$dailyIndexingCount}/{$this->googleIndexingClient->getDailyLimit()}", 'google-indexing');
			return false;
		}

		$process = new GoogleIndexProcess($googleIndex, GoogleIndexProcess::API_TYPE_INDEXING, GoogleIndexProcess::REQUEST_TYPE_URL_UPDATED);
		$siteUrl = $this->getSiteUrlForSearchConsole($googleIndex->getWebsiteDomain());

		$process->setRequestData(Json::encode([
			'url' => $googleIndex->getUrl(),
			'siteUrl' => $siteUrl,
		]));

		$result = $this->googleIndexingClient->requestIndexing($googleIndex->getUrl());

		if ($result['success']) {
			$process->markSuccess($result['status_code'], $result['response']);
			$googleIndex->markIndexingRequested();
			$googleIndex->setLastIndexingStatus(GoogleIndex::STATUS_REQUESTED);

			Debugger::log("Indexing request sent for: " . $googleIndex->getUrl(), 'google-indexing');
		} else {
			$process->markError($result['status_code'], $result['error'] ?? 'Unknown error', $result['response']);
			$googleIndex->setLastErrorMessage($result['error'] ?? 'Unknown error');

			Debugger::log("Indexing request failed for: " . $googleIndex->getUrl() . " - " . $result['error'], 'google-indexing');
		}

		$this->entityManager->persist($process);
		$this->entityManager->persist($googleIndex);
		$this->entityManager->flush();

		return $result['success'];
	}

	public function checkIndexingStatus(GoogleIndex $googleIndex): bool
	{
		$dailyStatusCheckCount = $this->googleIndexProcessRepository->countStatusCheckApiRequestsToday();

		if ($dailyStatusCheckCount >= 1800) {
			Debugger::log("Daily rate limit reached for status check. Daily: {$dailyStatusCheckCount}/1800", 'google-indexing');
			return false;
		}

		$process = new GoogleIndexProcess($googleIndex, GoogleIndexProcess::API_TYPE_SEARCH_CONSOLE, GoogleIndexProcess::REQUEST_TYPE_STATUS_CHECK);
		$siteUrl = $this->getSiteUrlForSearchConsole($googleIndex->getWebsiteDomain());

		$process->setRequestData(Json::encode([
			'url' => $googleIndex->getUrl(),
			'siteUrl' => $siteUrl,
		]));

		$result = $this->googleIndexingClient->checkIndexingStatus($googleIndex->getUrl(), $siteUrl);

		if ($result['success']) {
			$process->markSuccess($result['status_code'], $result['response']);
			$googleIndex->setLastStatusCheckAt(new \DateTime());

			if ($result['is_indexed']) {
				$googleIndex->setIndexed(true);
				$googleIndex->setLastIndexingStatus(GoogleIndex::STATUS_INDEXED);
				Debugger::log("URL is indexed: " . $googleIndex->getUrl(), 'google-indexing');
			} else {
				$googleIndex->setLastIndexingStatus(GoogleIndex::STATUS_NOT_INDEXED);
				Debugger::log("URL is not indexed yet: " . $googleIndex->getUrl(), 'google-indexing');
			}
		} else {
			$process->markError($result['status_code'], $result['error'] ?? 'Unknown error', $result['response']);
			$googleIndex->setLastErrorMessage($result['error'] ?? 'Unknown error');

			Debugger::log("Status check failed for: " . $googleIndex->getUrl() . " - " . $result['error'], 'google-indexing');
		}

		$this->entityManager->persist($process);
		$this->entityManager->persist($googleIndex);
		$this->entityManager->flush();

		return $result['success'];
	}

	private function getSiteUrlForSearchConsole(string $domain): string
	{
		$parsedUrl = parse_url($domain);

		if (!$parsedUrl) {
			return $domain;
		}

		$scheme = $parsedUrl['scheme'] ?? 'https';
		$host = $parsedUrl['host'] ?? '';

		// Domain property formát (fungující konfigurace)
		$cleanHost = str_replace('www.', '', $host);
		return 'sc-domain:' . $cleanHost;
	}

	public function getStatistics(): array
	{
		return [
			'pending_indexing' => $this->googleIndexRepository->countPendingIndexing(),
			'indexed' => $this->googleIndexRepository->countIndexed(),
			'leaflets_added_today' => $this->googleIndexRepository->countLeafletsAddedToday(),
			'api_requests_today' => $this->googleIndexProcessRepository->countApiRequestsToday(),
			'indexing_requests_today' => $this->googleIndexProcessRepository->countIndexingApiRequestsToday(),
			'status_check_requests_today' => $this->googleIndexProcessRepository->countStatusCheckApiRequestsToday(),
			'daily_limit' => $this->googleIndexingClient->getDailyLimit(),
		];
	}

	public function findByUrl(string $url): ?GoogleIndex
	{
		return $this->googleIndexRepository->findByUrl($url);
	}

	public function save(GoogleIndex $googleIndex): void
	{
		$this->entityManager->persist($googleIndex);
		$this->entityManager->flush();
	}

	public function countLeafletsAddedToday(): int
	{
		return $this->googleIndexRepository->countLeafletsAddedToday();
	}

	public function findUrlsToIndex(int $limit = 50): array
	{
		return $this->googleIndexRepository->findUrlsToIndex($limit);
	}

	public function findUrlsToCheckStatus(int $limit = 50): array
	{
		return $this->googleIndexRepository->findUrlsToCheckStatus($limit);
	}
}
