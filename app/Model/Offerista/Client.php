<?php

namespace <PERSON><PERSON><PERSON>\Model\Offerista;

use DateTimeInterface;
use GuzzleHttp\Psr7\Request;
use <PERSON><PERSON><PERSON>\Model\Datadog\DatadogClient;
use <PERSON><PERSON><PERSON>\Model\Websites\Entities\Website;
use Nette\Utils\Json;
use <PERSON>\Debugger;

class Client
{
	private const TRACKING_URL = 'https://tracking.offerista.com';

	public const CREDENTIALS_OFERTO = [
		'cs' => ['A6CFB388AB336418', '416b870bad5c7ed381e2034c97496487'],
		'hu' => ['995C5B9A354E8F6E', '552caac6fff9e94f108a8c6afb73a577'],
		'pl' => ['B9FFE7F7DD5417DC', '********************************'],
		'fr' => ['9664659A32BDD5AB', '********************************'],
		'de' => ['BCDC66AFC833C98E', '321ac11f8b1b3d1192a39710c2dd2ec6'],
		'it' => ['A878FCDC83E5D1F7', '********************************'],
		'ro' => ['B61714DAF4105944', 'debb3c2116beddf15be27f2cefc2d55b'],
		'sk' => ['83B2F9D6F7E3E7A4', '2ba6262b90a69b72ef3ff2ee0cc7ecde'],
	];

	public const CREDENTIALS_KAUFINO = [
		'cs' => ['AF6C9BEB1AC6BF76', '7b7af9c21d9cb55653f05cc4ffedf5f3'],
		'hu' => ['AA439450D24847BE', 'bdd0571eeabe9a78cb13979497c87920'],
		'pl' => ['B66C9EECC2C8E604', '5be470e0b8cda32c6603e53410383c92'],
		'fr' => ['8BF6701EEE9EF1D4', '9db0e4e56871b24b1fa3c7052a962412'],
		'de' => ['826B8FE16E013CEE', '34e2c50e78b678417a459fa74acea354'],
		'it' => ['AB3F3FD92088B183', '0f3a51c00e913b2fd592df9fb3cf0b7c'],
		'ro' => ['82585B0E785656FF', 'c2bc77828e850db25442da8c7ab8f2ba'],
		'sk' => ['BFCADBC06B5423B7', 'cab4d1398c99056a7f8f4ea5d057f2c8'],
	];

	public const UTM_SOURCE_OFERTO = 'mroferto';
	public const UTM_SOURCE_KAUFINO = 'kaufino';

	private const TRACK_BROCHURE_IMPRESSION = 'BROCHURE_IMPRESSION';
	private const TRACK_BROCHURE_CLICK = 'BROCHURE_CLICK';
	private const TRACK_BROCHUREPAGE_VIEW = 'BROCHUREPAGE_VIEW';
	private const TRACK_BROCHUREPAGE_DURATION = 'BROCHUREPAGE_DURATION';
	public const TRACK_BROCHURE_CLICKOUT = 'BROCHUREPAGE_CLICKOUT';

	public const LOCATION_STRATEGY_FALLBACK = 'FALLBACK';
	public const LOCATION_STRATEGY_IP = 'IP_ADDRESS';
	public const LOCATION_STRATEGY_DEVICE = 'DEVICE';
	public const LOCATION_STRATEGY_USER_INPUT = 'USER_INPUT';

	private const LOCATION = [
		'latitude' => 47.1611615,
		'longitude' => 19.5057541,
		'strategy' => 'FALLBACK',
	];

	private string $userName;
	private string $password;
	private DatadogClient $datadogClient;

	public function __construct(string $userName, string $password, DatadogClient $datadogClient)
	{
		$this->userName = $userName;
		$this->password = $password;
		$this->datadogClient = $datadogClient;
	}

	public function trackBrochureImpression(Website $website, string $utmSource, string $userId, string $uuid, int $brochureId, ?array $location = null)
	{
		$this->track($website, $utmSource, self::TRACK_BROCHURE_IMPRESSION, $userId, $location ?? self::LOCATION, $brochureId, null, $uuid);
	}

	public function trackBrochureClickout(Website $website, string $utmSource, string $userId, string $uuid, int $brochureId, string $target, int $brochurePage, ?array $location = null)
	{
		$this->track($website, $utmSource, self::TRACK_BROCHURE_CLICKOUT, $userId, $location ?? self::LOCATION, $brochureId, null, $uuid, '', $target, $brochurePage);
	}

	public function trackBrochureClick(Website $website, string $utmSource, string $userId, string $uuid, int $brochureId, ?array $location = null)
	{
		$this->track($website, $utmSource, self::TRACK_BROCHURE_CLICK, $userId, $location ?? self::LOCATION, $brochureId, null, $uuid);
	}

	public function trackBrochurePageView(Website $website, string $utmSource, string $userId, string $uuid, int $brochureId, ?array $location, int $pageId = 1)
	{
		$this->track($website, $utmSource, self::TRACK_BROCHUREPAGE_VIEW, $userId, $location ?? self::LOCATION, $brochureId, $pageId, $uuid);
	}

	public function trackBrochurePageViewDuration(Website $website, string $utmSource, string $userId, string $uuid, int $brochureId, ?array $location, int $pageId = 1, float $duration = 0, ?string $relatedTrackUuid = null): void
	{
		$this->track($website, $utmSource, self::TRACK_BROCHUREPAGE_DURATION, $userId, $location ?? self::LOCATION, $brochureId, $pageId, $uuid, $relatedTrackUuid, duration: $duration);
	}

	private function track(
		Website $website,
		string $utmSource,
		string $type,
		string $userId,
		array $location,
		int $brochureId,
		?int $pageId = null,
		?string $trackUid = null,
		?string $relatedTrackUid = null,
		?string $target = null,
		?int $brochurePage = null,
		?float $duration = null
	): void {
		$client = $this->createHttpClient($utmSource);

		$body = $this->prepareBody($type, $userId, $location, $brochureId, $pageId, $trackUid, $relatedTrackUid, $target, $brochurePage, $duration);

		try {
			$response = $client->post('/trackings', ['json' => $body]);

			if ($duration) {
				Debugger::log($response->getStatusCode(), 'offerista-duration-tracking-response');
			}

			if ($type === self::TRACK_BROCHURE_CLICK) {
				Debugger::log($body, 'offerista-brochure-click-log');

				if (in_array($brochureId, [5455819, 5460008, 5466454, 5474079, 5477470, 5481710, 5485209, 5489060, 5492962])) {
					Debugger::log($body, 'offerista-brochure-click-2-log');
				}
			}
		} catch (\Exception $e) {
			if ($type === self::TRACK_BROCHUREPAGE_DURATION) {
				Debugger::log($e->getMessage(), 'offerista-tracking-error');
			}
		}
	}

	private function createHttpClient(string $utmSource): \GuzzleHttp\Client
	{
		return new \GuzzleHttp\Client([
			'base_uri' => self::TRACKING_URL,
			'query' => ['utm_source' => $utmSource],
			'auth' => [$this->userName, $this->password],
		]);
	}

	private function prepareBody(
		string $type,
		string $userId,
		array $location,
		int $brochureId,
		?int $pageId,
		?string $trackUid,
		?string $relatedTrackUid,
		?string $target,
		?int $brochurePage = null,
		?float $duration = null
	): array {
		$body = [
			'createdAt' => (new \DateTime())->format(DateTimeInterface::ATOM),
			'type' => $type,
			'userUuid' => $userId,
			'location' => $location,
			'objectId' => $brochureId,
		];

		if ($pageId) {
			$body['brochurePage'] = $pageId;
		}

		if ($trackUid) {
			$body['trackUuid'] = $trackUid;
		}

		if ($relatedTrackUid) {
			$body['relatedTrackUuid'] = $relatedTrackUid;
		}

		if ($target) {
			$body['target'] = $target;
		}

		if ($brochurePage) {
			$body['brochurePage'] = $brochurePage;
		}

		if ($duration) {
			$body['duration'] = $duration;
		}

		return $body;
	}

	private function logTracking(Website $website, array $body, int $statusCode, int $brochureId, string $type): void
	{
		$logData = [
			'STATUS_CODE' => $statusCode,
			'EVENT_TYPE' => $type,
			'STATUS' => $statusCode >= 200 && $statusCode < 300 ? 'info' : 'error',
			'RESOURCE_ID' => $brochureId,
			'LOCALE' => $website->getLocalization()->getLocale(),
		];

		$this->datadogClient->sendLog('OfferistaTrackingAPI', $website->getHost(), Json::encode($body), $logData);
	}

	public function findUserLocationByIp(string $ip)
	{
		try {
			$client = new \GuzzleHttp\Client([
				'base_uri' => 'https://delivery.offerista.com',
				'verify' => false,
			]);

			$response = $client->post('/location', [
				'headers' => [
					'Authorization' => 'Basic QjYxNzE0REFGNDEwNTk0NDpkZWJiM2MyMTE2YmVkZGYxNWJlMjdmMmNlZmMyZDU1Yg==',
					'Accept' => 'application/json',
					'Content-Type' => 'application/x-www-form-urlencoded',
				],
				'form_params' => [
					'ip' => $ip,
				],
			]);

			return Json::decode($response->getBody());
		} catch (\Exception $e) {
			return null;
		}
	}
}
